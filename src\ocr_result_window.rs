use crate::ocr::OcrResult;
use crate::svg_icons::SvgIconManager;
use crate::types::ToolbarButton;
use anyhow::Result;
use windows::Win32::Foundation::*;
use windows::Win32::Graphics::Direct2D::Common::*;
use windows::Win32::Graphics::Direct2D::*;
use windows::Win32::Graphics::Gdi::*;
use windows::Win32::UI::Input::KeyboardAndMouse::*;
use windows::Win32::UI::WindowsAndMessaging::*;

/// OCR 结果显示窗口
pub struct OcrResultWindow {
    hwnd: HWND,
    text_edit: HWND, // 可选择的文本编辑控件
    ocr_results: Vec<OcrResult>,
    image_bitmap: Option<HBITMAP>,
    image_width: i32,
    image_height: i32,
    font: HFONT,
    text_area_rect: RECT,      // 文字显示区域
    window_width: i32,         // 窗口宽度
    window_height: i32,        // 窗口高度
    is_no_text_detected: bool, // 是否是"未识别到文字"状态

    // Direct2D 资源用于绘制工具栏
    d2d_factory: ID2D1Factory,
    render_target: ID2D1HwndRenderTarget,
    svg_icon_manager: SvgIconManager,
    toolbar_bg_brush: ID2D1SolidColorBrush,
    button_hover_brush: ID2D1SolidColorBrush,
    text_brush: ID2D1SolidColorBrush,

    // 工具栏状态
    toolbar_height: i32,
    hovered_button: ToolbarButton,
    is_dragging: bool,
    drag_start_pos: POINT,
}

impl OcrResultWindow {
    /// 创建并显示 OCR 结果窗口
    pub fn show(
        image_data: Vec<u8>,
        ocr_results: Vec<OcrResult>,
        selection_rect: RECT,
    ) -> Result<()> {
        unsafe {
            // 注册窗口类
            let class_name = windows::core::w!("OcrResultWindow");
            let instance = windows::Win32::System::LibraryLoader::GetModuleHandleW(None)?;

            // 使用与托盘相同的图标
            let icon = crate::system_tray::create_default_icon().unwrap_or_else(|_| {
                // 如果加载失败，使用默认应用程序图标
                LoadIconW(None, IDI_APPLICATION).unwrap_or_default()
            });

            let window_class = WNDCLASSW {
                lpfnWndProc: Some(Self::window_proc),
                hInstance: instance.into(),
                lpszClassName: class_name,
                hCursor: LoadCursorW(None, IDC_ARROW)?,
                hbrBackground: HBRUSH((COLOR_WINDOW.0 + 1) as *mut _),
                style: CS_HREDRAW | CS_VREDRAW,
                hIcon: icon, // 设置窗口图标
                ..Default::default()
            };

            RegisterClassW(&window_class);

            // 从 BMP 数据获取实际图片尺寸
            let (bitmap, actual_width, actual_height) = Self::create_bitmap_from_data(&image_data)?;

            // 获取屏幕尺寸
            let screen_width = GetSystemMetrics(SM_CXSCREEN);
            let screen_height = GetSystemMetrics(SM_CYSCREEN);

            // 右边文字区域宽度（固定350像素）
            let text_area_width = 350;

            // 图像保持原始尺寸，不进行缩放
            let display_image_width = actual_width;
            let display_image_height = actual_height;

            // 左边图像区域宽度（实际显示宽度 + 边距，比图片大一圈）
            let image_area_width = display_image_width + 40; // 左右各20像素边距
            // 总窗口宽度
            let window_width = image_area_width + text_area_width + 20; // 中间分隔20像素

            // 使用Windows API获取准确的窗口装饰尺寸
            let caption_height = GetSystemMetrics(SM_CYCAPTION); // 标题栏高度
            let border_height = GetSystemMetrics(SM_CYBORDER); // 边框高度
            let frame_height = GetSystemMetrics(SM_CYFRAME); // 窗口框架高度

            // 计算窗口装饰的总高度
            let window_decoration_height =
                caption_height + (border_height * 2) + (frame_height * 2);

            // 增加更多的内容边距，确保有足够空间
            let content_padding = 120; // 上下各60像素边距，增加空间

            // 窗口总高度 = 图像高度 + 窗口装饰高度 + 内容边距
            // 再额外增加一些空间以确保不被截断
            let extra_space = 50;
            let window_height =
                display_image_height + window_decoration_height + content_padding + extra_space;

            // 计算窗口位置（在截图区域附近显示，避免超出屏幕）

            let mut window_x = selection_rect.right + 20; // 在截图区域右侧
            let mut window_y = selection_rect.top;

            // 确保窗口不超出屏幕边界
            if window_x + window_width > screen_width {
                window_x = selection_rect.left - window_width - 20; // 放在左侧
                if window_x < 0 {
                    window_x = 50; // 如果左侧也放不下，就放在屏幕左边
                }
            }
            if window_y + window_height > screen_height {
                window_y = screen_height - window_height - 50;
                if window_y < 0 {
                    window_y = 50;
                }
            }

            // 创建无边框窗口
            let hwnd = CreateWindowExW(
                WS_EX_TOPMOST | WS_EX_TOOLWINDOW, // 置顶且不在任务栏显示
                class_name,
                windows::core::w!("识别结果"),    // 窗口标题
                WS_POPUP | WS_VISIBLE,           // 无边框弹出窗口样式
                window_x,
                window_y,
                window_width,
                window_height,
                None,
                None,
                Some(instance.into()),
                None,
            )?;

            // 位图已经在上面创建了
            let width = actual_width;
            let height = actual_height;

            // 工具栏高度
            let toolbar_height = 40;

            // 初始化 Direct2D
            let d2d_factory: ID2D1Factory = D2D1CreateFactory(
                D2D1_FACTORY_TYPE_SINGLE_THREADED,
                None,
            )?;

            // 创建渲染目标
            let render_target_properties = D2D1_RENDER_TARGET_PROPERTIES {
                r#type: D2D1_RENDER_TARGET_TYPE_DEFAULT,
                pixelFormat: D2D1_PIXEL_FORMAT {
                    format: windows::Win32::Graphics::Dxgi::Common::DXGI_FORMAT_B8G8R8A8_UNORM,
                    alphaMode: D2D1_ALPHA_MODE_PREMULTIPLIED,
                },
                dpiX: 0.0,
                dpiY: 0.0,
                usage: D2D1_RENDER_TARGET_USAGE_NONE,
                minLevel: D2D1_FEATURE_LEVEL_DEFAULT,
            };

            let hwnd_render_target_properties = D2D1_HWND_RENDER_TARGET_PROPERTIES {
                hwnd,
                pixelSize: D2D_SIZE_U {
                    width: window_width as u32,
                    height: window_height as u32,
                },
                presentOptions: D2D1_PRESENT_OPTIONS_NONE,
            };

            let render_target = d2d_factory.CreateHwndRenderTarget(
                &render_target_properties,
                &hwnd_render_target_properties,
            )?;

            // 创建画刷
            let toolbar_bg_brush = render_target.CreateSolidColorBrush(
                &D2D1_COLOR_F {
                    r: 0.95,
                    g: 0.95,
                    b: 0.95,
                    a: 1.0,
                },
                None,
            )?;

            let button_hover_brush = render_target.CreateSolidColorBrush(
                &D2D1_COLOR_F {
                    r: 0.85,
                    g: 0.85,
                    b: 0.85,
                    a: 1.0,
                },
                None,
            )?;

            let text_brush = render_target.CreateSolidColorBrush(
                &D2D1_COLOR_F {
                    r: 0.0,
                    g: 0.0,
                    b: 0.0,
                    a: 1.0,
                },
                None,
            )?;

            // 初始化SVG图标管理器
            let mut svg_icon_manager = SvgIconManager::new();
            if let Err(e) = svg_icon_manager.load_icons() {
                eprintln!("Failed to load SVG icons: {}", e);
            }

            // 创建微软雅黑字体
            let font_name: Vec<u16> = "微软雅黑"
                .encode_utf16()
                .chain(std::iter::once(0))
                .collect();
            let font = CreateFontW(
                24,                                        // 字体高度（增大字体）
                0,                                         // 字体宽度（0表示自动）
                0,                                         // 文本角度
                0,                                         // 基线角度
                FW_NORMAL.0 as i32,                        // 字体粗细
                0,                                         // 斜体
                0,                                         // 下划线
                0,                                         // 删除线
                DEFAULT_CHARSET,                           // 字符集
                OUT_DEFAULT_PRECIS,                        // 输出精度
                CLIP_DEFAULT_PRECIS,                       // 裁剪精度
                DEFAULT_QUALITY,                           // 输出质量
                (DEFAULT_PITCH.0 | FF_DONTCARE.0) as u32,  // 字体间距和族
                windows::core::PCWSTR(font_name.as_ptr()), // 字体名称
            );

            // 计算文字显示区域，为工具栏留出空间
            let text_padding_left = 20; // 左侧padding
            let text_padding_right = 20; // 右侧padding
            let text_padding_top = toolbar_height + 15; // 顶部padding，为工具栏留出空间
            let text_padding_bottom = 15; // 底部padding

            let text_area_rect = RECT {
                left: image_area_width + text_padding_left,
                top: text_padding_top,
                right: window_width - text_padding_right,
                bottom: window_height - text_padding_bottom,
            };

            // 创建文本编辑控件（可选择的文本）
            let text_edit = CreateWindowExW(
                WS_EX_CLIENTEDGE,
                windows::core::w!("EDIT"),
                windows::core::w!(""),
                WS_CHILD | WS_VISIBLE | WS_VSCROLL | WINDOW_STYLE(0x0004 | 0x0010), // ES_MULTILINE | ES_READONLY
                text_area_rect.left,
                text_area_rect.top,
                text_area_rect.right - text_area_rect.left,
                text_area_rect.bottom - text_area_rect.top,
                Some(hwnd),
                None,
                Some(instance.into()),
                None,
            )?;

            // 设置文本编辑控件的字体
            SendMessageW(
                text_edit,
                WM_SETFONT,
                Some(WPARAM(font.0 as usize)),
                Some(LPARAM(1)),
            );

            // 合并所有OCR结果为文本
            let mut all_text = String::new();
            let mut is_no_text_detected = false;

            for (i, result) in ocr_results.iter().enumerate() {
                if i > 0 {
                    all_text.push_str("\r\n"); // Windows换行符
                }

                // 检查是否是"未识别到文字"的特殊情况
                if result.text == "未识别到任何文字" && result.confidence == 0.0 {
                    is_no_text_detected = true;
                    all_text.push_str("未识别到任何文字");
                } else {
                    all_text.push_str(&result.text);
                }
            }

            if all_text.trim().is_empty() {
                all_text = "未识别到文本内容".to_string();
                is_no_text_detected = true;
            }

            // 设置文本内容
            let text_wide: Vec<u16> = all_text.encode_utf16().chain(std::iter::once(0)).collect();
            SetWindowTextW(text_edit, windows::core::PCWSTR(text_wide.as_ptr()))?;

            // 创建窗口实例
            let window = Self {
                hwnd,
                text_edit,
                ocr_results,
                image_bitmap: Some(bitmap),
                image_width: width,
                image_height: height,
                font,
                text_area_rect,
                window_width,
                window_height,
                is_no_text_detected,
                d2d_factory,
                render_target,
                svg_icon_manager,
                toolbar_bg_brush,
                button_hover_brush,
                text_brush,
                toolbar_height,
                hovered_button: ToolbarButton::None,
                is_dragging: false,
                drag_start_pos: POINT { x: 0, y: 0 },
            };

            // 将窗口实例指针存储到窗口数据中
            let window_ptr = Box::into_raw(Box::new(window));
            SetWindowLongPtrW(hwnd, GWLP_USERDATA, window_ptr as isize);

            // 显示窗口
            ShowWindow(hwnd, SW_SHOW);
            UpdateWindow(hwnd);

            Ok(())
        }
    }

    /// 从 BMP 数据创建位图
    fn create_bitmap_from_data(bmp_data: &[u8]) -> Result<(HBITMAP, i32, i32)> {
        unsafe {
            if bmp_data.len() < 54 {
                return Err(anyhow::anyhow!("BMP 数据太小"));
            }

            // 解析 BMP 头部获取尺寸信息
            let width =
                i32::from_le_bytes([bmp_data[18], bmp_data[19], bmp_data[20], bmp_data[21]]);
            let height =
                i32::from_le_bytes([bmp_data[22], bmp_data[23], bmp_data[24], bmp_data[25]]).abs(); // 取绝对值，因为可能是负数

            // 获取屏幕 DC
            let screen_dc = GetDC(None);

            // 创建兼容的内存 DC
            let mem_dc = CreateCompatibleDC(Some(screen_dc));

            // 创建兼容的位图
            let bitmap = CreateCompatibleBitmap(screen_dc, width, height);

            // 选择位图到内存 DC
            let old_bitmap = SelectObject(mem_dc, bitmap.into());

            // 创建 BITMAPINFO 结构
            let bitmap_info = BITMAPINFO {
                bmiHeader: BITMAPINFOHEADER {
                    biSize: std::mem::size_of::<BITMAPINFOHEADER>() as u32,
                    biWidth: width,
                    biHeight: -height, // 负值表示自顶向下
                    biPlanes: 1,
                    biBitCount: 32,
                    biCompression: BI_RGB.0,
                    biSizeImage: 0,
                    biXPelsPerMeter: 0,
                    biYPelsPerMeter: 0,
                    biClrUsed: 0,
                    biClrImportant: 0,
                },
                bmiColors: [RGBQUAD::default(); 1],
            };

            // 获取像素数据（跳过 BMP 文件头）
            let pixel_data = &bmp_data[54..];

            // 将像素数据设置到位图
            SetDIBits(
                Some(mem_dc),
                bitmap,
                0,
                height as u32,
                pixel_data.as_ptr() as *const _,
                &bitmap_info,
                DIB_RGB_COLORS,
            );

            // 清理
            SelectObject(mem_dc, old_bitmap);
            DeleteDC(mem_dc);
            ReleaseDC(None, screen_dc);

            Ok((bitmap, width, height))
        }
    }

    /// 绘制窗口内容
    fn paint(&self) -> Result<()> {
        unsafe {
            let mut ps = PAINTSTRUCT::default();
            let hdc = BeginPaint(self.hwnd, &mut ps);

            let mut rect = RECT::default();
            GetClientRect(self.hwnd, &mut rect)?;

            // 使用Direct2D绘制
            self.render_target.BeginDraw();

            // 清除背景为白色
            self.render_target.Clear(Some(&D2D1_COLOR_F {
                r: 1.0,
                g: 1.0,
                b: 1.0,
                a: 1.0,
            }));

            // 绘制工具栏
            self.draw_toolbar()?;

            let hr = self.render_target.EndDraw(None, None);
            if hr.is_err() {
                // 如果Direct2D绘制失败，回退到GDI绘制
                let white_brush = CreateSolidBrush(COLORREF(0x00FFFFFF));
                FillRect(hdc, &rect, white_brush);
                DeleteObject(white_brush.into());
            }

            // 绘制窗口边框
            let border_pen = CreatePen(PS_SOLID, 2, COLORREF(0x00CCCCCC));
            let old_pen = SelectObject(hdc, border_pen.into());
            let old_brush = SelectObject(hdc, GetStockObject(NULL_BRUSH));

            Rectangle(hdc, rect.left, rect.top, rect.right, rect.bottom);

            SelectObject(hdc, old_pen);
            SelectObject(hdc, old_brush);
            DeleteObject(border_pen.into());

            // 设置文本颜色为黑色
            SetTextColor(hdc, COLORREF(0x00000000));
            SetBkMode(hdc, TRANSPARENT);

            // 选择微软雅黑字体
            let old_font = SelectObject(hdc, self.font.into());

            // 使用预计算的布局
            let image_area_width = self.text_area_rect.left - 10;

            // 绘制图像区域边框
            let image_rect = RECT {
                left: 10,
                top: 10,
                right: image_area_width - 10,
                bottom: self.window_height - 10,
            };

            let border_pen = CreatePen(PS_SOLID, 1, COLORREF(0x00CCCCCC));
            let old_pen = SelectObject(hdc, border_pen.into());
            let old_brush = SelectObject(hdc, GetStockObject(NULL_BRUSH));

            Rectangle(
                hdc,
                image_rect.left,
                image_rect.top,
                image_rect.right,
                image_rect.bottom,
            );

            SelectObject(hdc, old_pen);
            SelectObject(hdc, old_brush);
            DeleteObject(border_pen.into());

            // 绘制实际的截图图像
            if let Some(bitmap) = self.image_bitmap {
                // 创建内存 DC 来绘制位图
                let mem_dc = CreateCompatibleDC(Some(hdc));
                let old_bitmap = SelectObject(mem_dc, bitmap.into());

                // 计算图像显示区域（保持宽高比，居中显示）
                let available_width = image_area_width - 40;
                let available_height = self.window_height - 40;

                let scale_x = available_width as f32 / self.image_width as f32;
                let scale_y = available_height as f32 / self.image_height as f32;
                let scale = scale_x.min(scale_y).min(1.0); // 不放大

                let scaled_width = (self.image_width as f32 * scale) as i32;
                let scaled_height = (self.image_height as f32 * scale) as i32;

                let x_offset = 20 + (available_width - scaled_width) / 2;
                let y_offset = 20 + (available_height - scaled_height) / 2;

                // 使用 StretchBlt 绘制缩放的图像
                StretchBlt(
                    hdc,
                    x_offset,
                    y_offset,
                    scaled_width,
                    scaled_height,
                    Some(mem_dc),
                    0,
                    0,
                    self.image_width,
                    self.image_height,
                    SRCCOPY,
                );

                SelectObject(mem_dc, old_bitmap);
                DeleteDC(mem_dc);
            } else {
                // 如果没有位图，显示提示文字
                let image_text = "截图图像\n(加载失败)";
                let mut image_text_rect = RECT {
                    left: 20,
                    top: 30,
                    right: image_area_width - 20,
                    bottom: 100,
                };

                let mut image_text_wide: Vec<u16> = image_text
                    .encode_utf16()
                    .chain(std::iter::once(0))
                    .collect();
                DrawTextW(
                    hdc,
                    &mut image_text_wide,
                    &mut image_text_rect,
                    DT_LEFT | DT_TOP | DT_WORDBREAK,
                );
            }

            // 文本内容现在由Edit控件处理，无需手动绘制

            // 恢复原来的字体
            SelectObject(hdc, old_font);

            EndPaint(self.hwnd, &ps);

            Ok(())
        }
    }

    /// 绘制工具栏
    fn draw_toolbar(&self) -> Result<()> {
        unsafe {
            // 绘制工具栏背景
            let toolbar_rect = D2D_RECT_F {
                left: 0.0,
                top: 0.0,
                right: self.window_width as f32,
                bottom: self.toolbar_height as f32,
            };

            self.render_target.FillRectangle(&toolbar_rect, &self.toolbar_bg_brush);

            // 绘制工具栏按钮
            let button_size = 24.0;
            let button_spacing = 8.0;
            let start_x = 10.0;
            let button_y = (self.toolbar_height as f32 - button_size) / 2.0;

            // 定义工具栏按钮
            let toolbar_buttons = [
                ToolbarButton::Pin,
                ToolbarButton::Save,
                ToolbarButton::ExtractText,
            ];

            for (i, &button) in toolbar_buttons.iter().enumerate() {
                let button_x = start_x + (i as f32) * (button_size + button_spacing);
                let button_rect = D2D_RECT_F {
                    left: button_x,
                    top: button_y,
                    right: button_x + button_size,
                    bottom: button_y + button_size,
                };

                // 如果是悬停按钮，绘制背景
                if self.hovered_button == button {
                    self.render_target.FillRectangle(&button_rect, &self.button_hover_brush);
                }

                // 绘制SVG图标
                if let Ok(Some(icon_bitmap)) = self.svg_icon_manager.render_icon_to_bitmap(
                    button,
                    &self.render_target,
                    button_size as u32,
                    Some((64, 64, 64)), // 深灰色
                ) {
                    self.render_target.DrawBitmap(
                        &icon_bitmap,
                        Some(&button_rect),
                        1.0,
                        D2D1_BITMAP_INTERPOLATION_MODE_LINEAR,
                        None,
                    );
                }
            }

            // 绘制窗口控制按钮（最小化、最大化、关闭）
            self.draw_window_controls()?;

            Ok(())
        }
    }

    /// 绘制窗口控制按钮
    fn draw_window_controls(&self) -> Result<()> {
        unsafe {
            let button_size = 24.0;
            let button_spacing = 4.0;
            let right_margin = 10.0;
            let button_y = (self.toolbar_height as f32 - button_size) / 2.0;

            // 计算按钮位置（从右到左：关闭、最大化、最小化）
            let close_x = self.window_width as f32 - right_margin - button_size;
            let maximize_x = close_x - button_size - button_spacing;
            let minimize_x = maximize_x - button_size - button_spacing;

            // 绘制最小化按钮
            let minimize_rect = D2D_RECT_F {
                left: minimize_x,
                top: button_y,
                right: minimize_x + button_size,
                bottom: button_y + button_size,
            };

            if self.hovered_button == ToolbarButton::None { // 这里需要扩展枚举来支持窗口控制按钮
                // 暂时不绘制悬停效果
            }

            // 绘制最小化图标
            if let Ok(Some(icon_bitmap)) = self.svg_icon_manager.render_icon_to_bitmap(
                ToolbarButton::Minimize,
                &self.render_target,
                button_size as u32,
                Some((64, 64, 64)),
            ) {
                self.render_target.DrawBitmap(
                    &icon_bitmap,
                    Some(&minimize_rect),
                    1.0,
                    D2D1_BITMAP_INTERPOLATION_MODE_LINEAR,
                    None,
                );
            }

            // 绘制最大化按钮
            let maximize_rect = D2D_RECT_F {
                left: maximize_x,
                top: button_y,
                right: maximize_x + button_size,
                bottom: button_y + button_size,
            };

            if let Ok(Some(icon_bitmap)) = self.svg_icon_manager.render_icon_to_bitmap(
                ToolbarButton::Maximize,
                &self.render_target,
                button_size as u32,
                Some((64, 64, 64)),
            ) {
                self.render_target.DrawBitmap(
                    &icon_bitmap,
                    Some(&maximize_rect),
                    1.0,
                    D2D1_BITMAP_INTERPOLATION_MODE_LINEAR,
                    None,
                );
            }

            // 绘制关闭按钮
            let close_rect = D2D_RECT_F {
                left: close_x,
                top: button_y,
                right: close_x + button_size,
                bottom: button_y + button_size,
            };

            if let Ok(Some(icon_bitmap)) = self.svg_icon_manager.render_icon_to_bitmap(
                ToolbarButton::Close,
                &self.render_target,
                button_size as u32,
                Some((200, 64, 64)), // 红色
            ) {
                self.render_target.DrawBitmap(
                    &icon_bitmap,
                    Some(&close_rect),
                    1.0,
                    D2D1_BITMAP_INTERPOLATION_MODE_LINEAR,
                    None,
                );
            }

            Ok(())
        }
    }

    /// 处理鼠标按下事件
    fn handle_mouse_down(&mut self, x: i32, y: i32, hwnd: HWND) {
        // 检查是否点击在工具栏区域
        if y < self.toolbar_height {
            let button = self.get_toolbar_button_at_position(x, y);
            if button != ToolbarButton::None {
                self.handle_toolbar_button_click(button, hwnd);
                return;
            }

            // 检查窗口控制按钮
            let control_button = self.get_window_control_button_at_position(x, y);
            if control_button != ToolbarButton::None {
                self.handle_window_control_button_click(control_button, hwnd);
                return;
            }

            // 在工具栏空白区域开始拖拽窗口
            self.is_dragging = true;
            self.drag_start_pos = POINT { x, y };
            unsafe {
                SetCapture(hwnd);
            }
        }
    }

    /// 处理鼠标抬起事件
    fn handle_mouse_up(&mut self, _x: i32, _y: i32, _hwnd: HWND) {
        if self.is_dragging {
            self.is_dragging = false;
            unsafe {
                ReleaseCapture();
            }
        }
    }

    /// 处理鼠标移动事件
    fn handle_mouse_move(&mut self, x: i32, y: i32, hwnd: HWND) {
        if self.is_dragging {
            // 拖拽窗口
            let dx = x - self.drag_start_pos.x;
            let dy = y - self.drag_start_pos.y;

            unsafe {
                let mut window_rect = RECT::default();
                GetWindowRect(hwnd, &mut window_rect);
                SetWindowPos(
                    hwnd,
                    None,
                    window_rect.left + dx,
                    window_rect.top + dy,
                    0,
                    0,
                    SWP_NOSIZE | SWP_NOZORDER,
                );
            }
        } else if y < self.toolbar_height {
            // 更新悬停按钮
            let new_hovered = self.get_toolbar_button_at_position(x, y);
            if new_hovered != self.hovered_button {
                self.hovered_button = new_hovered;
                unsafe {
                    InvalidateRect(Some(hwnd), None, FALSE.into());
                }
            }
        } else if self.hovered_button != ToolbarButton::None {
            // 鼠标离开工具栏区域
            self.hovered_button = ToolbarButton::None;
            unsafe {
                InvalidateRect(Some(hwnd), None, FALSE.into());
            }
        }
    }

    /// 窗口过程
    unsafe extern "system" fn window_proc(
        hwnd: HWND,
        msg: u32,
        wparam: WPARAM,
        lparam: LPARAM,
    ) -> LRESULT {
        unsafe {
            match msg {
                WM_PAINT => {
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &*window_ptr;
                        let _ = window.paint();
                    } else {
                        let mut ps = PAINTSTRUCT::default();
                        let _hdc = BeginPaint(hwnd, &mut ps);
                        EndPaint(hwnd, &ps);
                    }
                    LRESULT(0)
                }
                WM_LBUTTONDOWN => {
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &mut *window_ptr;
                        let x = (lparam.0 & 0xFFFF) as i16 as i32;
                        let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;
                        window.handle_mouse_down(x, y, hwnd);
                    }
                    LRESULT(0)
                }
                WM_LBUTTONUP => {
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &mut *window_ptr;
                        let x = (lparam.0 & 0xFFFF) as i16 as i32;
                        let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;
                        window.handle_mouse_up(x, y, hwnd);
                    }
                    LRESULT(0)
                }
                WM_MOUSEMOVE => {
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &mut *window_ptr;
                        let x = (lparam.0 & 0xFFFF) as i16 as i32;
                        let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;
                        window.handle_mouse_move(x, y, hwnd);
                    }
                    LRESULT(0)
                }
                // 移除自定义左键处理，使用标准窗口行为
                WM_RBUTTONUP => {
                    // 右键点击关闭窗口
                    PostMessageW(Some(hwnd), WM_CLOSE, WPARAM(0), LPARAM(0));
                    LRESULT(0)
                }
                WM_KEYDOWN => {
                    // 处理键盘按键
                    if wparam.0 == VK_ESCAPE.0 as usize {
                        // ESC 键关闭窗口
                        PostMessageW(Some(hwnd), WM_CLOSE, WPARAM(0), LPARAM(0));
                    }
                    LRESULT(0)
                }
                WM_CTLCOLOREDIT => {
                    // 处理Edit控件的颜色
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &*window_ptr;
                        let hdc = HDC(wparam.0 as *mut _);
                        let edit_hwnd = HWND(lparam.0 as *mut _);

                        // 检查是否是我们的文本编辑控件
                        if edit_hwnd == window.text_edit {
                            if window.is_no_text_detected {
                                // 设置灰色文本
                                SetTextColor(hdc, COLORREF(0x00808080)); // 灰色
                            } else {
                                // 设置正常黑色文本
                                SetTextColor(hdc, COLORREF(0x00000000)); // 黑色
                            }
                            SetBkMode(hdc, TRANSPARENT);
                            // 返回透明画刷
                            return LRESULT(GetStockObject(NULL_BRUSH).0 as isize);
                        }
                    }
                    DefWindowProcW(hwnd, msg, wparam, lparam)
                }
                WM_MOUSEWHEEL => {
                    // 将滚轮事件转发给文本编辑控件
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = &*window_ptr;
                        SendMessageW(window.text_edit, msg, Some(wparam), Some(lparam));
                    }
                    LRESULT(0)
                }
                WM_CLOSE => {
                    let window_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut Self;
                    if !window_ptr.is_null() {
                        let window = Box::from_raw(window_ptr);
                        // 清理字体资源
                        DeleteObject(window.font.into());
                        // 清理位图资源
                        if let Some(bitmap) = window.image_bitmap {
                            DeleteObject(bitmap.into());
                        }
                        SetWindowLongPtrW(hwnd, GWLP_USERDATA, 0);
                    }
                    DestroyWindow(hwnd);
                    LRESULT(0)
                }
                _ => DefWindowProcW(hwnd, msg, wparam, lparam),
            }
        }
    }

    /// 获取指定位置的工具栏按钮
    fn get_toolbar_button_at_position(&self, x: i32, y: i32) -> ToolbarButton {
        if y >= self.toolbar_height {
            return ToolbarButton::None;
        }

        let button_size = 24.0;
        let button_spacing = 8.0;
        let start_x = 10.0;

        let toolbar_buttons = [
            ToolbarButton::Pin,
            ToolbarButton::Save,
            ToolbarButton::ExtractText,
        ];

        for (i, &button) in toolbar_buttons.iter().enumerate() {
            let button_x = start_x + (i as f32) * (button_size + button_spacing);
            if x as f32 >= button_x && x as f32 <= button_x + button_size {
                return button;
            }
        }

        ToolbarButton::None
    }

    /// 获取指定位置的窗口控制按钮
    fn get_window_control_button_at_position(&self, x: i32, y: i32) -> ToolbarButton {
        if y >= self.toolbar_height {
            return ToolbarButton::None;
        }

        let button_size = 24.0;
        let button_spacing = 4.0;
        let right_margin = 10.0;

        let close_x = self.window_width as f32 - right_margin - button_size;
        let maximize_x = close_x - button_size - button_spacing;
        let minimize_x = maximize_x - button_size - button_spacing;

        let x_f = x as f32;

        if x_f >= close_x && x_f <= close_x + button_size {
            ToolbarButton::Close
        } else if x_f >= maximize_x && x_f <= maximize_x + button_size {
            ToolbarButton::Maximize
        } else if x_f >= minimize_x && x_f <= minimize_x + button_size {
            ToolbarButton::Minimize
        } else {
            ToolbarButton::None
        }
    }

    /// 处理工具栏按钮点击
    fn handle_toolbar_button_click(&self, button: ToolbarButton, hwnd: HWND) {
        match button {
            ToolbarButton::Pin => {
                // TODO: 实现置顶功能
                unsafe {
                    let current_style = GetWindowLongW(hwnd, GWL_EXSTYLE);
                    if current_style & WS_EX_TOPMOST.0 as i32 != 0 {
                        // 取消置顶
                        SetWindowPos(hwnd, Some(HWND_NOTOPMOST), 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
                    } else {
                        // 设置置顶
                        SetWindowPos(hwnd, Some(HWND_TOPMOST), 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
                    }
                }
            }
            ToolbarButton::Save => {
                // TODO: 实现保存功能
                // 可以复制文本到剪贴板或保存到文件
            }
            ToolbarButton::ExtractText => {
                // TODO: 实现文本提取功能
                // 可以重新进行OCR识别
            }
            _ => {}
        }
    }

    /// 处理窗口控制按钮点击
    fn handle_window_control_button_click(&self, button: ToolbarButton, hwnd: HWND) {
        unsafe {
            match button {
                ToolbarButton::Minimize => {
                    ShowWindow(hwnd, SW_MINIMIZE);
                }
                ToolbarButton::Maximize => {
                    // 切换最大化/还原状态
                    if IsZoomed(hwnd).as_bool() {
                        ShowWindow(hwnd, SW_RESTORE);
                    } else {
                        ShowWindow(hwnd, SW_MAXIMIZE);
                    }
                }
                ToolbarButton::Close => {
                    PostMessageW(Some(hwnd), WM_CLOSE, WPARAM(0), LPARAM(0));
                }
                _ => {}
            }
        }
    }
}
